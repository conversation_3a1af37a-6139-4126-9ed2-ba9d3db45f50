# -*- coding: utf-8 -*-
"""
Created on Sat Dec  2 11:38:52 2023

@author: Administrator
"""


# -*- coding: utf-8 -*-
"""
Created on Wed Nov 29 10:55:16 2023

@author: Administrator
"""
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib import rcParams          #修改全局字体样式
rcParams['font.family'] = 'SimHei'       #修改为黑体
from scipy.signal import convolve,lfilter


def get_data(filepath,test_len=0):
    with open(filepath,'r') as file:
        content = file.read()
    data_list = content.split()
        
    data_list = list(map(int,data_list)) 
    return data_list
    
def draw_gray(gray_image,name):
    ####画灰度图
    plt.figure(figsize = (10,10))#(figsize = (800,1))
    #aspect:调整像素块的比例   interpolation = 'kaiser'选择插值方法 cmap='jet','gray'
    plt.imshow(gray_image,cmap='gray',aspect='equal',interpolation='spline36')   
#    plt.colorbar()    
    #ax.set_yticklabels([]) 
    #plt.savefig('gray.png',dpi=800) #
    plt.title("{}".format(name))
    plt.show()

def sxx_normal(data,max_np=255):  
    data_np = data.copy()
    v_min = np.min(data)
    v_max = np.max(data)
    for i in range(0,len(data),1):
        data_np[i] = (data[i]-v_min)/(v_max-v_min)*max_np 
    return data_np      

def filter_3sigma(data,n=3,times=1):
    rows = np.size(data,0)
    #重复处理
    for j in range(times):
        #单次处理，按行处理
        for i in range(rows):
            arr = data[i]
        
            mean = arr.mean()
            std = np.sqrt( ((arr-mean)**2).sum() / len(arr) )
            
            
            max_range = mean + n*std
            min_range = mean - n*std
            if(min_range < 0):
                min_range = arr.min()
            data[i] = np.clip(data[i],min_range,max_range)
            
        
# *****************生成按子文件夹排序的路径*****************#
import os
import re
def find_subfolders_txt(parent_folder):
    sub_folders = []
    for name in os.listdir(parent_folder):
        # #找子文件夹下的所有txt
        # if(os.path.isdir(os.path.join(parent_folder,name))):
        #     temp_path = parent_folder + fr"\{name}\2.txt"
        
        # #找文件夹下的所有txt
        if(name.endswith('.txt')):
            temp_path = parent_folder + fr"\{name}"
            #添加进列表中
            sub_folders.append(temp_path)
    # print(sub_folders)      
            
    def extract_number(folder_name):
        numbers = re.findall(r'\d+',folder_name)
        print(numbers)
        return int(numbers[2]) if numbers else 0

    # sub_folders.sort(key = extract_number)
    return sub_folders
#********************读取数据***********************#
def ReadTxt_Space(filepath,test_len=0):
    with open(filepath,'r') as file:
        content = file.read()
    data_list = content.split()
    # data_list = [int(x,16) for x in data_list]  #16进制
    data_list = list(map(int,data_list))           #十进制
    return data_list
#############################********主函数********############################
T,R = 600,440 # 230,120
head = 0
if __name__ == "__main__":
    
    # parent_folder = r"F:\0_CBS\2_PY代码\10W样机试验数据\线对数据" #
    parent_folder = r"F:\0_CBS\2_PY代码\10W样机试验数据\20250816\xd" #
    
    sub_folders = find_subfolders_txt(parent_folder)
    xuhao = 0 *2
    
    data_list = ReadTxt_Space(sub_folders[0+xuhao]) 
    data = np.array(data_list)[head:-(len(data_list)-T*R)+head]

    data_list2 = ReadTxt_Space(sub_folders[1+xuhao])#[:-43]
    data2 = np.array(data_list2)[head:-(len(data_list2)-T*R)+head]
    
    print(sub_folders[0+xuhao])
    print(sub_folders[1+xuhao])

    import cv2
    
    #1 对线对数据预处理
    def Preprocessing_Data(data,T,R):
        #1.求四分位值以下的矩阵Data_q25，并归一化
        Value_q25 = np.percentile(data, 25)
        Data_q25 = np.where(data <= Value_q25,data,0).reshape(T,R,order='C')
        Data_q25 = sxx_normal(Data_q25,255)

        #2.Data_q25矩阵十字膨胀
        kernel_q25 = cv2.getStructuringElement(cv2.MORPH_CROSS, (3,3))
        Cross_Data_q25 = cv2.dilate(np.uint16(Data_q25), kernel_q25)

        Data = data.reshape(T,R,order='C')
        Data = Data-Cross_Data_q25
        Data = sxx_normal(Data,255)
        return Data
    
    #2 合并2路数据并处理
    def Merge_2Data(data1,data2):
        #1.合并数据，并3sigma
        data_add = ((data1 + data2)/2)
        filter_3sigma(data_add,1,1)
   
        #2.双边模糊+CLAHE
        bilate = cv2.bilateralFilter(np.float32(data_add), 7,3,11)
        clahe = cv2.createCLAHE(clipLimit=5.0, tileGridSize=(10,10)) 
        Clahed = clahe.apply(np.uint8(bilate))
        #3.归一化
        Clahed = sxx_normal(Clahed,255)
        return Clahed
    

    # 3.Frangi2D用： ---------- 自己实现的高斯导数 ----------
    def gaussian_filter_cv(img, sigma, order=(0, 0)):
        """
        用 OpenCV + 差分计算高斯导数
        order=(2,0) -> d^2/dx^2
        order=(0,2) -> d^2/dy^2
        order=(1,1) -> d^2/dxdy
        """
        ksize = int(6*sigma + 1) | 1          # 保证奇数
        smooth = cv2.GaussianBlur(img, (ksize, ksize), sigma)
    
        if order == (2, 0):                   # d^2/dx^2
            kern = np.array([1, -2, 1], dtype=np.float32)
            return cv2.filter2D(smooth, -1, kern[..., None])
        elif order == (0, 2):                 # d^2/dy^2
            kern = np.array([1, -2, 1], dtype=np.float32)
            return cv2.filter2D(smooth, -1, kern[None, ...])
        elif order == (1, 1):                 # d^2/dxdy
            kx = np.array([1, 0, -1], dtype=np.float32) * 0.5
            ky = np.array([1, 0, -1], dtype=np.float32) * 0.5
            dx  = cv2.filter2D(smooth, -1, kx[..., None])
            dxy = cv2.filter2D(dx, -1, ky[None, ...])
            return dxy
        else:
            return smooth
    
    # 3. Frangi 滤波增强曲线---------- Frangi2D ----------
    def frangi2d_cv2(img, sigma_start=1, sigma_end=5, sigma_step=1,
                    beta1=0.5, beta2=15):
        img = img.astype(np.float32)
        vessel = np.zeros_like(img)
    
        for sigma in range(sigma_start, sigma_end+1, sigma_step):
            Dxx = gaussian_filter_cv(img, sigma, order=(2,0))
            Dyy = gaussian_filter_cv(img, sigma, order=(0,2))
            Dxy = gaussian_filter_cv(img, sigma, order=(1,1))
    
            # 计算特征值
            tmp = np.sqrt((Dxx - Dyy)**2 + 4*Dxy**2 + 1e-12)
            mu1 = 0.5*(Dxx + Dyy + tmp)
            mu2 = 0.5*(Dxx + Dyy - tmp)
    
            # Frangi 响应
            Rb = (mu1 / (mu2 + 1e-12)) ** 2
            S2 = mu1**2 + mu2**2
            vessel_sigma = (1 - np.exp(-Rb/(2*beta1**2))) *  np.exp(-S2/(2*beta2**2))
            vessel = np.maximum(vessel, vessel_sigma)
        return vessel

    # 4. 获取vesselness曲线掩膜
    def vesselness_cv2(data1,data2,T,R):
        Data1 = Preprocessing_Data(data1,T,R)
        Data2 = Preprocessing_Data(data2,T,R)
        Data_Merge = Merge_2Data(Data1,Data2)
        vesselness = frangi2d_cv2(Data_Merge,sigma_start=1,sigma_end=4,sigma_step=1,beta1=15, beta2=1)
        vesselness = cv2.normalize(vesselness, None, 0, 255, cv2.NORM_MINMAX, dtype=cv2.CV_8U)
        _,mask = cv2.threshold(vesselness, 0.1, 1, cv2.THRESH_BINARY)
        mask = mask*255
        return mask
    
    # 5. 线对增强最终
    def XD_Enhanced(mask): 
        #1. 高斯模糊
        blurred = cv2.GaussianBlur(mask, (11, 11), 0)
        #2. Otsu 阈值化
        _, thresholded = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
        #3. 形态学闭运算连接断裂
        kernel_er = np.ones((1,1),np.uint8)
        eroded = cv2.erode(thresholded, kernel_er, iterations=1)
        #4. 膨胀反色
        re_eroded = 255-eroded 
        
        # 1. 高斯模糊
        blurred = cv2.GaussianBlur(re_eroded, (5, 5), 0)
        # 2. Otsu 阈值化
        _, thresholded = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)
        draw_gray(thresholded,"thresholded")
        # 单次膨胀（无法连接）
        kernel = np.ones((3,3), np.uint8)
        dil1 = cv2.dilate(thresholded, kernel)
        draw_gray(dil1,"dil1")
        
    mask = vesselness_cv2(data,data2,T,R)
    XD_Enhanced(mask)
    