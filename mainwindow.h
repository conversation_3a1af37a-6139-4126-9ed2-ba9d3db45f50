#ifndef MAINWINDOW_H
#define MAINWINDOW_H


#include <QMainWindow>
#include <opencv2/opencv.hpp>
#include <QFile>
#include <QTextStream>
#include <QString>
#include <QStringList>
#include <QVector>
#include <QDebug>
#include <algorithm>

#include <QImage>
#include <QPixmap>


namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = 0);
    ~MainWindow();

private:
    Ui::MainWindow *ui;

    QVector<int> ReadTxt_Space(const QString& filepath);
    QVector<QVector<int>> ConvertToMatrix(const QVector<int>& data, int rows, int cols);

    QPixmap MatrixToPixmap(const QVector<QVector<int>>& matrix);
    QPixmap MatrixToGrayPixmap(const QVector<QVector<int>>& mat);
};

#endif // MAINWINDOW_H
