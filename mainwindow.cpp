#include "mainwindow.h"
#include "ui_mainwindow.h"



MainWindow::MainWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    QVector<int> dataList;
    QVector<QVector<int>> drawVector;

    dataList = ReadTxt_Space("D:/Users/<USER>/qt_project/XD/2025_08_16_08_31_48.txt");
    int T = 600;
    int R = 440;
    //转为二维矩阵
    drawVector = ConvertToMatrix(dataList,T,R);
    //将矩阵转成qpixmap类型
    QPixmap result_pic;
//    result_pic = MatrixToPixmap(drawVector);
//    result_pic = MatrixToPixmap0(drawVector);
    result_pic = MatrixToGrayPixmap(drawVector);
    //将qpixmap显示在qlabel控件中
    ui->label->setPixmap(result_pic);
    int test = 440;

}

MainWindow::~MainWindow()
{
    delete ui;
}

QVector<QVector<int>> MainWindow::ConvertToMatrix(const QVector<int>& data, int rows, int cols)
{
    QVector<QVector<int>> matrix;
    QVector<int> data2;

    if(data.size() < rows * cols)
    {
        qDebug() << "数据不足，无法填充" << rows << "x" << cols << "矩阵";
        return matrix;
    }
    else
    {
        data2 = data.mid(0,rows * cols);
    }

    for(int i=0; i < rows; ++i)
    {
        QVector<int> row;
        for (int j=0; j< cols; ++j)
        {
            int index =i*cols +j;
            row.append(data2[index]);
        }
        matrix.append(row);
    }

    return matrix;

}

QPixmap MainWindow::MatrixToGrayPixmap(const QVector<QVector<int>>& mat)
{
    if(mat.isEmpty()|| mat[0].isEmpty())
        return QPixmap();

    const int rows = mat.size();
    const int cols = mat.first().size();


    int minVal = mat[0][0], maxVal = mat[0][0];
    for(const auto& row:mat)
        for (int v : row)
        {
            minVal = qMin(minVal,v);
            maxVal = qMax(maxVal,v);
        }
    const double scale = (maxVal == minVal) ? 0.0 : 255.0 / (maxVal - minVal);


    QByteArray buf(rows * cols,Qt::Uninitialized);
    uchar* dst = reinterpret_cast<uchar*>(buf.data());
    for(int r=0; r<rows; ++r)
    {
        const QVector<int>& row = mat[r];
        for(int c=0; c<cols; ++c)
            *dst++ = static_cast<uchar>((row[c] - minVal) * scale + 0.5);
    }

    QImage img(reinterpret_cast<const uchar*>(buf.constData()),
               cols,rows,
               cols,
               QImage::Format_Grayscale8,
               [](void* p){delete static_cast<QByteArray*>(p);},
                new QByteArray(std::move(buf))
              );


    return QPixmap::fromImage(img.copy());
}

QPixmap MainWindow::MatrixToPixmap(const QVector<QVector<int>>& matrix)
{
    if(matrix.isEmpty()|| matrix[0].isEmpty())
    {
        return QPixmap();
    }

    int rows = matrix.size();
    int cols = matrix[0].size();

    int minVal = matrix[0][0];
    int maxVal = matrix[0][0];

    for(int i=0; i<rows; ++i)
    {
        for (int j=0; j<cols; ++j)
        {
            minVal = std::min(minVal,matrix[i][j]);
            maxVal = std::max(maxVal,matrix[i][j]);
        }

    }

    QImage image(cols,rows,QImage::Format_Grayscale8);

    if(minVal == maxVal)
    {
        image.fill(128);
        return QPixmap::fromImage(image);
    }

    for(int i=0;i<rows;++i)
    {
        for(int j=0;j<cols;++j)
        {
            int normalized = 255*(matrix[i][j] - minVal) / (maxVal-minVal);

            normalized = std::max(0,std::min(255,normalized));

            image.setPixel(j,i,normalized);

        }
    }

    return QPixmap::fromImage(image);
}

QVector<int> MainWindow::ReadTxt_Space(const QString& filepath)
{
    QVector<int> dataList;

    qDebug() << "进入函数";
    // 打开文件
    QFile file(filepath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "无法打开文件:" << filepath;
        return dataList;
    }

    // 读取文件内容
    QTextStream in(&file);
    QString content = in.readAll();
    file.close();

    // 分割内容
    QStringList stringList = content.split(QRegExp("\\s+"), QString::SkipEmptyParts);
    qDebug() << "分割内容";
    // 转换为整数
    for (const QString& str : stringList) {
        bool ok;
        int value = str.toInt(&ok); // 十进制转换
        // int value = str.toInt(&ok, 16); // 16进制转换（注释掉）
        if (ok) {
            dataList.append(value);
        } else {
            qDebug() << "无法转换为整数:" << str;
        }
    }


    qDebug() << dataList;
    return dataList;
}

//QVector<int> MainWindow::Preprocessing_Data(QVector & filepath)
//{

//}
//def Preprocessing_Data(data,T,R):
//    #1.求四分位值以下的矩阵Data_q25，并归一化
//    Value_q25 = np.percentile(data, 25)
//    Data_q25 = np.where(data <= Value_q25,data,0).reshape(T,R,order='C')
//    Data_q25 = sxx_normal(Data_q25,255)

//    #2.Data_q25矩阵十字膨胀
//    kernel_q25 = cv2.getStructuringElement(cv2.MORPH_CROSS, (3,3))
//    Cross_Data_q25 = cv2.dilate(np.uint16(Data_q25), kernel_q25)

//    Data = data.reshape(T,R,order='C')
//    Data = Data-Cross_Data_q25
//    Data = sxx_normal(Data,255)
//    return Data
